from pydantic import BaseModel, Field, field_validator
from backend.app.modules.auth.models import UserRole

class RegisterIn(BaseModel):
    phone: str = Field(max_length=20, description="手机号")
    # code: str = Field(max_length=6, description="验证码")
    password: str = Field(max_length=255, description="密码")
    role: UserRole = Field(default=UserRole.USER, description="用户角色")
    
    @field_validator("phone")
    def check_phone(cls, v):
        if not v.isdigit():
            raise ValueError("手机号必须为数字")
        return v
    @field_validator("password")
    @classmethod
    def check_password(cls, v):
        if v.replace(" ", "") == "":
            raise ValueError("密码不能包含空格")
        return v

# 登录请求
class LoginIn(BaseModel):
    phone: str = Field(max_length=20, description="手机号")
    password: str = Field(max_length=255, description="密码")
    
    @field_validator("phone")
    def check_phone(cls, v):
        if not v.isdigit():
            raise ValueError("手机号必须为数字")
        return v
    @field_validator("password")
    @classmethod
    def check_password(cls, v):
        if v.replace(" ", "") == "":
            raise ValueError("密码不能包含空格")
        return v

# 用户信息
class UserOut(BaseModel):
    id: int = Field(description="用户ID")
    phone: str = Field(description="手机号")
    user_name: str = Field(description="用户名")
    role: str = Field(description="用户角色")
    is_active: bool = Field(description="是否启用")

# OAuth2令牌响应
class TokenResponse(BaseModel):
    access_token: str = Field(description="访问令牌")
    refresh_token: str = Field(description="刷新令牌")
    token_type: str = Field(default="bearer", description="令牌类型")
    expires_in: int = Field(description="令牌过期时间（秒）")
    user: UserOut = Field(description="用户信息")

# OAuth2令牌响应（仅访问令牌）
class AccessTokenResponse(BaseModel):
    access_token: str = Field(description="访问令牌")
    token_type: str = Field(default="bearer", description="令牌类型")
    expires_in: int = Field(description="令牌过期时间（秒）")

# Bearer Token请求头
class BearerToken(BaseModel):
    token: str = Field(description="Bearer令牌")

# 用户信息响应（包含令牌）
class AuthResponse(BaseModel):
    user: UserOut = Field(description="用户信息")
    tokens: TokenResponse = Field(description="令牌信息")
